/* SailorPay Unified Variables CSS */

:root {
    /* Primary Colors */
    --primary-color: #0078d4;
    --primary-dark: #0066b3;
    --primary-light: #1a88d9;

    /* Neutral Colors */
    --neutral-100: #ffffff;
    --neutral-200: #f8f9fa;
    --neutral-300: #e9ecef;
    --neutral-400: #dee2e6;
    --neutral-500: #adb5bd;
    --neutral-600: #6c757d;
    --neutral-700: #495057;
    --neutral-800: #343a40;
    --neutral-900: #212529;

    /* Accent Colors */
    --accent-color: #17a2b8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;

    /* Typography */
    --font-family-base: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-family-heading: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-size-base: 16px;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 700;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-width: 1px;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Layout */
    --container-max-width: 1200px;
    --header-height: 70px;
    --footer-height: auto;

    /* Z-index layers */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;
}

/* Common Elements */
.wrapper {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Unified Header Styles */
.site-header {
    background-color: var(--primary-dark);
    color: var(--neutral-100);
    position: sticky;
    top: 0;
    z-index: var(--z-index-sticky);
    box-shadow: var(--shadow-md);
}

/* Unified Footer Styles */
#footer-bottom {
    background-color: var(--primary-dark);
    color: var(--neutral-100);
    padding: var(--spacing-xl) 0;
}

/* Navbar Margin */
.navbar-margin {
    height: 0; /* Removed height to eliminate the gap */
    background-color: #005fa3; /* Exact color match as requested */
    margin: 0; /* Remove all margins */
    padding: 0; /* Remove all padding */
    opacity: 1; /* Full opacity for consistent color */
    transition: all var(--transition-slow);
    position: relative; /* Position relative to allow proper stacking */
    z-index: 10; /* Ensure it's above other elements */
}

.scrolled + .navbar-margin {
    opacity: 0.9;
    height: 0;
}
