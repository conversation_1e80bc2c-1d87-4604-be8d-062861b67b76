/* Imprint Page Styles */

.imprint-section {
    padding: 60px 0;
}

.imprint-section h1 {
    color: #226191;
    font-size: 36px;
    margin-bottom: 30px;
    text-align: center;
}

.imprint-content {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.imprint-content h2 {
    color: #226191;
    font-size: 24px;
    margin: 0 0 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.imprint-content h3 {
    color: #226191;
    font-size: 20px;
    margin: 20px 0 10px;
}

.imprint-content p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.company-info {
    margin-bottom: 30px;
}

.company-info p {
    margin-bottom: 8px;
}

.company-info a,
.contact-details a {
    color: #226191;
    text-decoration: none;
    font-weight: 600;
}

.company-info a:hover,
.contact-details a:hover {
    text-decoration: underline;
}

.contact-details {
    margin-bottom: 30px;
}

.contact-details p {
    margin-bottom: 8px;
}

/* Contact Form Styles */
.contact-form-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
}

.contact-form {
    flex: 1;
    min-width: 300px;
}

.contact-direct {
    flex: 0 0 auto;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-group {
    margin-bottom: 15px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
}

.submit-btn {
    background-color: #226191;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.submit-btn:hover {
    background-color: #184a73;
}

.contact-direct p {
    margin-bottom: 15px;
}

.contact-direct .social-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

@media (max-width: 768px) {
    .contact-form-container {
        flex-direction: column;
    }

    .contact-direct {
        margin-top: 20px;
    }
}

.contact-options {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 30px;
}

.contact-option {
    flex: 1;
    min-width: 300px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    border-radius: 4px;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.social-links .linkedin-link {
    background-color: #0077B5;
}

.social-links .linkedin-link:hover {
    background-color: #005e93;
}

.social-links .whatsapp-link {
    background-color: #25D366;
}

.social-links .whatsapp-link:hover {
    background-color: #128C7E;
}

.social-links a i {
    margin-right: 8px;
    font-size: 16px;
}

@media (max-width: 768px) {
    .imprint-section {
        padding: 40px 0;
    }

    .imprint-section h1 {
        font-size: 28px;
    }

    .imprint-content {
        padding: 20px;
    }

    .contact-options {
        flex-direction: column;
    }

    .contact-option {
        min-width: 100%;
    }
}
