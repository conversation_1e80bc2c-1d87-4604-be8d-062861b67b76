/* Modern Enhancements for Sailor<PERSON>ay Improved */

:root {
    /* Primary Colors */
    --primary-color: #0078d4;
    --primary-dark: #005fa3;
    --primary-light: #2b88d8;

    /* Secondary Colors */
    --secondary-color: #226191;
    --secondary-dark: #1a4971;
    --secondary-light: #3a7fb1;

    /* Neutral Colors */
    --neutral-100: #ffffff;
    --neutral-200: #f8f9fa;
    --neutral-300: #e8eaed;
    --neutral-400: #dadce0;
    --neutral-500: #9aa0a6;
    --neutral-600: #5f6368;
    --neutral-700: #3c4043;
    --neutral-800: #202124;
    --neutral-900: #000000;

    /* Accent Colors */
    --accent-blue: #4285f4;
    --accent-red: #ea4335;
    --accent-yellow: #fbbc05;
    --accent-green: #34a853;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
}

/* Modern Body Styles */
body {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--neutral-800);
    background-color: var(--neutral-100);
    transition: background-color var(--transition-normal);
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Archivo Black', sans-serif;
    color: var(--primary-color);
    margin-bottom: 20px;
    line-height: 1.3;
}

h1 {
    font-size: 2.75rem;
    margin-bottom: 30px;
    font-weight: 700;
}

h2 {
    font-size: 2.25rem;
    margin-bottom: 25px;
    font-weight: 600;
}

h3 {
    font-size: 1.75rem;
    margin-bottom: 20px;
    font-weight: 600;
}

p {
    margin-bottom: 20px;
    color: var(--neutral-700);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* Modern Buttons */
.button {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--neutral-100);
    padding: 12px 28px;
    border-radius: var(--radius-md);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    cursor: pointer;
    text-decoration: none;
}

.button:hover {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    color: var(--neutral-100);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
}

.button:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: var(--radius-full);
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.button:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(100, 100);
        opacity: 0;
    }
}

/* Modern Header */
.site-header {
    background-color: var(--primary-dark);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all var(--transition-normal);
    color: var(--neutral-100);
}

/* Navbar margin styles moved to variables.css */

.top-bar {
    background-color: #0078d4; /* Match the original SailorPay blue */
    color: var(--neutral-100);
    padding: 8px 0;
}

.top-bar a {
    color: var(--neutral-100);
    text-decoration: none;
    transition: opacity var(--transition-fast);
}

.top-bar a:hover {
    opacity: 0.9;
    text-decoration: underline;
}

/* Modern Navigation */
.main-navigation {
    display: flex;
}

.hamburger {
    display: block;
    background: none;
    border: none;
    color: var(--neutral-100);
    font-size: 24px;
    cursor: pointer;
    transition: color var(--transition-fast);
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
}

.hamburger:hover {
    color: var(--neutral-300);
}

.hamburger-box {
    width: 30px;
    height: 24px;
    display: inline-block;
    position: relative;
}

.hamburger-inner {
    display: block;
    top: 50%;
    margin-top: -2px;
}

.hamburger-inner, .hamburger-inner::before, .hamburger-inner::after {
    width: 30px;
    height: 3px;
    background-color: var(--neutral-100);
    border-radius: 4px;
    position: absolute;
    transition-property: transform;
    transition-duration: 0.15s;
    transition-timing-function: ease;
}

.hamburger-inner::before, .hamburger-inner::after {
    content: "";
    display: block;
}

.hamburger-inner::before {
    top: -10px;
}

.hamburger-inner::after {
    bottom: -10px;
}

.hamburger.is-active .hamburger-inner {
    transform: rotate(45deg);
}

.hamburger.is-active .hamburger-inner::before {
    top: 0;
    opacity: 0;
}

.hamburger.is-active .hamburger-inner::after {
    bottom: 0;
    transform: rotate(-90deg);
}

.main-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-menu li {
    margin-left: 25px;
    position: relative;
}

.main-menu li a {
    color: var(--neutral-700);
    font-weight: 500;
    padding: 8px 0;
    position: relative;
    transition: color var(--transition-fast);
}

.main-menu li a:hover {
    color: var(--primary-color);
}

.main-menu li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

.main-menu li a:hover::after {
    width: 100%;
}

/* Modern Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    color: var(--neutral-100);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/pattern.svg');
    opacity: 0.05;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 30px;
    color: var(--neutral-700);
}

.hero-image {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    transition: transform var(--transition-normal);
}

.hero-image:hover {
    transform: translateY(-5px);
}

/* Modern Feature Boxes */
.feature-box {
    background-color: var(--neutral-100);
    border-radius: var(--radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    height: 100%;
    transition: all var(--transition-normal);
    border: 1px solid var(--neutral-300);
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.feature-box h3 {
    margin-top: 20px;
    color: var(--primary-color);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--neutral-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Modern Form Elements */
input, textarea, select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--neutral-400);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 14px;
    transition: all var(--transition-fast);
    background-color: var(--neutral-100);
    color: var(--neutral-800);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
}

.form-group {
    margin-bottom: 20px;
}

.submit-btn {
    background-color: var(--primary-color);
    color: var(--neutral-100);
    border: none;
    padding: 12px 25px;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.submit-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Modern Footer */
.site-footer {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--neutral-100);
    padding: 0;
}

.footer-menus {
    padding: 60px 0 40px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.widget-title {
    color: var(--neutral-100);
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 20px;
    letter-spacing: 1px;
    display: block;
    margin-top: 30px;
    position: relative;
    padding-bottom: 10px;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--neutral-300);
}

.site-footer .menu {
    list-style: none;
    margin: 0 0 30px 0;
    padding: 0;
}

.site-footer .menu li {
    margin-bottom: 12px;
}

.site-footer .menu li a {
    color: var(--neutral-300);
    font-size: 14px;
    transition: all var(--transition-fast);
    position: relative;
    padding-left: 0;
}

.site-footer .menu li a:hover {
    color: var(--neutral-100);
    padding-left: 5px;
}

#footer-bottom {
    padding: 30px 0;
    background-color: var(--primary-dark);
    color: var(--neutral-100);
}

.payment-logos {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

.payment-logo {
    width: 60px;
    height: 40px;
    margin-left: 15px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transition: transform var(--transition-fast);
}

.payment-logo:hover {
    transform: translateY(-2px);
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background-color: var(--primary-dark);
    z-index: 99;
    padding: 80px 30px 30px;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu .menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-menu .menu-item {
    margin-bottom: 20px;
}

.mobile-menu .menu-item a {
    color: var(--neutral-100);
    font-size: 18px;
    text-decoration: none;
    display: block;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.mobile-menu .menu-item a:hover {
    color: var(--neutral-300);
    padding-left: 5px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .main-menu {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .site-footer .col {
        width: 50%;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .site-footer .col {
        width: 100%;
    }

    .payment-logos {
        justify-content: center;
        margin-top: 30px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 1s ease-in-out;
}

.slide-up {
    animation: slideUp 0.8s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-in-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Modern Banner */
.full-width-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--neutral-100);
    padding: 40px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.full-width-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/wave-pattern.svg');
    opacity: 0.1;
    z-index: 1;
}

.banner-content {
    position: relative;
    z-index: 2;
}

.banner-title {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--neutral-100);
}

.banner-subtitle {
    font-size: 1.25rem;
    margin-bottom: 0;
    color: var(--neutral-200);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Modern Card Styles */
.card {
    background-color: var(--neutral-100);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    height: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 25px;
}

.card-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.card-text {
    color: var(--neutral-700);
    margin-bottom: 20px;
}

.card-link {
    color: var(--primary-color);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.card-link i {
    margin-left: 5px;
    transition: transform var(--transition-fast);
}

.card-link:hover i {
    transform: translateX(5px);
}

/* Modern Testimonial */
.testimonial {
    background-color: var(--neutral-100);
    border-radius: var(--radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    position: relative;
    margin-top: 40px;
    border: 1px solid var(--neutral-300);
}

.testimonial::before {
    content: '"';
    position: absolute;
    top: -30px;
    left: 30px;
    font-size: 80px;
    color: var(--primary-light);
    font-family: serif;
    line-height: 1;
    opacity: 0.3;
}

.testimonial-content {
    font-style: italic;
    margin-bottom: 20px;
    color: var(--neutral-700);
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    margin-right: 15px;
    object-fit: cover;
}

.testimonial-info h4 {
    margin: 0;
    font-size: 16px;
    color: var(--neutral-800);
}

.testimonial-info p {
    margin: 0;
    font-size: 14px;
    color: var(--neutral-600);
}

/* Modern Stats */
.stats-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 40px;
}

.stat-item {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: 20px;
    margin: 10px;
    background-color: var(--neutral-100);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1rem;
    color: var(--neutral-700);
    margin: 0;
}

/* Modern Timeline */
.timeline {
    position: relative;
    max-width: 1200px;
    margin: 40px auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 2px;
    background-color: var(--neutral-400);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -1px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-item::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: var(--radius-full);
    top: 15px;
    z-index: 1;
}

.timeline-item:nth-child(odd)::after {
    right: -10px;
}

.timeline-item:nth-child(even)::after {
    left: -10px;
}

.timeline-content {
    padding: 20px;
    background-color: var(--neutral-100);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    position: relative;
}

.timeline-content h3 {
    margin-top: 0;
}

@media (max-width: 768px) {
    .timeline::after {
        left: 31px;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }

    .timeline-item:nth-child(even) {
        left: 0;
    }

    .timeline-item::after {
        left: 21px;
    }

    .timeline-item:nth-child(odd)::after {
        right: auto;
    }
}
