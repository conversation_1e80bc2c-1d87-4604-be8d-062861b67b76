<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="220" height="73" viewBox="0 0 220 73" xmlns="http://www.w3.org/2000/svg">
  <style>
    .background { fill: #226191; }
    .logo-text { font-family: 'Montserrat', sans-serif; font-size: 28px; fill: #ffffff; font-weight: 600; }
    .trademark { font-family: 'Montserrat', sans-serif; font-size: 10px; fill: #ffffff; font-weight: 400; }
    .icon { fill: #ffffff; }
  </style>
  <rect class="background" x="0" y="0" width="220" height="73" rx="0" ry="0" />
  <g transform="translate(30, 30)">
    <!-- Circular icon -->
    <circle class="icon" cx="0" cy="0" r="8" />
    <!-- Arcs around the circle -->
    <path class="icon" d="M0,-15 A15,15 0 0,1 13,7.5 L11.5,6.5 A13,13 0 0,0 0,-13 Z" />
    <path class="icon" d="M0,-20 A20,20 0 0,1 17.32,10 L15.82,9 A18,18 0 0,0 0,-18 Z" />
    <path class="icon" d="M0,-25 A25,25 0 0,1 21.65,12.5 L20.15,11.5 A23,23 0 0,0 0,-23 Z" />
  </g>
  <text x="60" y="45" class="logo-text">sailorpay</text>
  <text x="175" y="35" class="trademark">®</text>
</svg>
