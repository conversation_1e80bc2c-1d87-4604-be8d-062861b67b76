/* Fixes for SailorPay Improved */

/* Fix for Firefox */
@-moz-document url-prefix() {
    .button {
        padding: 11px 25px;
    }
}

/* Fix for IE */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .row {
        display: -ms-flexbox;
        -ms-flex-wrap: wrap;
    }
    
    .col {
        -ms-flex: 0 0 auto;
    }
    
    .col-1 { -ms-flex-preferred-size: 8.33%; }
    .col-2 { -ms-flex-preferred-size: 16.66%; }
    .col-3 { -ms-flex-preferred-size: 25%; }
    .col-4 { -ms-flex-preferred-size: 33.33%; }
    .col-5 { -ms-flex-preferred-size: 41.66%; }
    .col-6 { -ms-flex-preferred-size: 50%; }
    .col-7 { -ms-flex-preferred-size: 58.33%; }
    .col-8 { -ms-flex-preferred-size: 66.66%; }
    .col-9 { -ms-flex-preferred-size: 75%; }
    .col-10 { -ms-flex-preferred-size: 83.33%; }
    .col-11 { -ms-flex-preferred-size: 91.66%; }
    .col-12 { -ms-flex-preferred-size: 100%; }
}

/* Fix for Safari */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        .feature-box {
            -webkit-transform: translateZ(0);
        }
    }
}

/* Fix for Edge */
@supports (-ms-ime-align:auto) {
    .row {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
}

/* Fix for Chrome */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .button {
        -webkit-appearance: none;
    }
}

/* Fix for mobile devices */
@media (max-width: 768px) {
    input, select, textarea {
        font-size: 16px !important;
    }
}

/* Fix for high-resolution displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}
