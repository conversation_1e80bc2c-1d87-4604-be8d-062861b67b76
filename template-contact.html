<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - SailorPay</title>
    
    <!-- favicons-->
    <link rel="icon" type="image/svg+xml" href="images/logos/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="manifest" href="site.webmanifest">
    <link rel="mask-icon" href="safari-pinned-tab.svg" color="#226191">
    <meta name="msapplication-TileColor" content="#226191">
    <meta name="theme-color" content="#ffffff">
    
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Archivo+Black&display=swap">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- Main CSS -->
    <link rel="stylesheet" href="css/style.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/fixes.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/footer.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/navbar.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/common.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/icons.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/payment-logos.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/hero-banner.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/social-icons.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/contact-form.css" type="text/css" media="all">
</head>
<body>
    <div id="page" class="site">
        <header id="masthead" class="site-header">
            <div class="top-bar">
                <div class="wrapper">
                    <div class="top-contact-info">
                        <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        <a href="https://www.linkedin.com/company/sailorpay" target="_blank" class="linkedin-link"><i class="fab fa-linkedin-in"></i> LinkedIn</a>
                        <a href="https://wa.me/***********" target="_blank" class="whatsapp-link"><i class="fab fa-whatsapp"></i> WhatsApp</a>
                    </div>
                </div>
            </div>
            
            <button type="button" aria-label="Menu" aria-controls="navigation" class="btn slide-menu-control hamburger hamburger--spin" data-target="demo-2" data-action="toggle">
                <span class="hamburger-box">
                    <span class="hamburger-inner"></span>
                </span>
            </button>

            <nav class="slide-menu" id="demo-2">
                <div class="menu-main-container">
                    <ul id="primary-menu" class="menu">
                        <li class="menu-item"><a href="index.html">Home</a></li>
                        <li class="menu-item"><a href="services.html">Services</a></li>
                        <li class="menu-item"><a href="#">Solutions</a>
                            <ul class="sub-menu">
                                <li><a href="services.html#global-payments">Global Payments</a></li>
                                <li><a href="services.html#secure-transactions">Secure Transactions</a></li>
                                <li><a href="services.html#easy-integration">Easy Integration</a></li>
                            </ul>
                        </li>
                        <li class="menu-item current-menu-item"><a href="contact.html">Contact Us</a></li>
                    </ul>
                </div>
            </nav>

            <div class="wrapper">
                <div class="site-branding">
                    <a href="index.html">
                        <img src="images/logos/logo-main.svg" alt="SailorPay" width="220" height="73">
                    </a>
                </div>
                <nav id="site-navigation" class="main-navigation">
                    <div class="menu-main-container">
                        <ul id="primary-menu" class="menu">
                            <li class="menu-item"><a href="index.html">Home</a></li>
                            <li class="menu-item"><a href="services.html">Services</a></li>
                            <li class="menu-item"><a href="#">Solutions</a>
                                <ul class="sub-menu">
                                    <li><a href="services.html#global-payments">Global Payments</a></li>
                                    <li><a href="services.html#secure-transactions">Secure Transactions</a></li>
                                    <li><a href="services.html#easy-integration">Easy Integration</a></li>
                                </ul>
                            </li>
                            <li class="menu-item current-menu-item"><a href="contact.html">Contact Us</a></li>
                        </ul>
                    </div>
                </nav>
            </div>
        </header>

        <div id="content" class="site-content">
            <div id="primary" class="content-area">
                <main id="main" class="site-main">
                    <section class="page-header">
                        <div class="wrapper">
                            <h1>Contact Us</h1>
                            <p>Get in touch with our team for any inquiries or support</p>
                        </div>
                    </section>

                    <section class="contact-section">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignLeft col-6">
                                    <h2>Send Us a Message</h2>
                                    <form id="contact-form" class="contact-form" action="#" method="post">
                                        <div class="form-group">
                                            <label for="name">Your Name</label>
                                            <input type="text" id="name" name="name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="email">Email Address</label>
                                            <input type="email" id="email" name="email" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="subject">Subject</label>
                                            <input type="text" id="subject" name="subject" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="message">Message</label>
                                            <textarea id="message" name="message" rows="5" required></textarea>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="button">Send Message</button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col col--alignLeft col-6">
                                    <h2>Contact Information</h2>
                                    <div class="contact-info">
                                        <div class="contact-item">
                                            <i class="fas fa-envelope"></i>
                                            <div>
                                                <h3>Email</h3>
                                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                            </div>
                                        </div>
                                        <div class="contact-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <div>
                                                <h3>Address</h3>
                                                <p>123 Payment Street<br>Financial District<br>10000 City, Country</p>
                                            </div>
                                        </div>
                                        <div class="contact-item">
                                            <i class="fas fa-clock"></i>
                                            <div>
                                                <h3>Business Hours</h3>
                                                <p>Monday - Friday: 9:00 AM - 5:00 PM<br>Saturday & Sunday: Closed</p>
                                            </div>
                                        </div>
                                        <div class="contact-item">
                                            <i class="fab fa-whatsapp"></i>
                                            <div>
                                                <h3>WhatsApp</h3>
                                                <p><a href="https://wa.me/***********" target="_blank">+**************</a></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>

        <footer id="colophon" class="site-footer">
            <div class="wrapper">
                <div class="footer-top">
                    <div class="row">
                        <div class="col col-4">
                            <a href="index.html">
                                <img src="images/logos/logo-footer.svg" alt="SailorPay" width="150">
                            </a>
                            <p>SailorPay is a leading payment gateway provider offering secure and flexible payment solutions for businesses worldwide.</p>
                            <div class="payment-logos">
                                <div class="payment-logo visa-logo" title="Visa"></div>
                                <div class="payment-logo mastercard-logo" title="Mastercard"></div>
                                <div class="payment-logo pci-logo" title="PCI DSS Compliant"></div>
                            </div>
                        </div>
                        
                        <!-- Footer menu columns go here -->
                        
                    </div>
                </div>
                
                <div class="footer-bottom">
                    <div class="row">
                        <div class="col col-6">
                            <p>&copy; 2023 SailorPay. All rights reserved.</p>
                        </div>
                        <div class="col col-6 text-right">
                            <p>Navigating Payments, Your Way</p>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript for menu functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hamburger menu toggle
            const menuToggle = document.querySelector('.hamburger');
            const slideMenu = document.getElementById('demo-2');
            
            if (menuToggle && slideMenu) {
                menuToggle.addEventListener('click', function() {
                    menuToggle.classList.toggle('is-active');
                    slideMenu.classList.toggle('is-active');
                });
            }
            
            // Mobile dropdown menu
            const mobileMenuItems = document.querySelectorAll('.slide-menu ul li');
            
            mobileMenuItems.forEach(function(item) {
                if (item.querySelector('.sub-menu')) {
                    item.addEventListener('click', function(e) {
                        if (e.target === item.querySelector('a') || e.target === item) {
                            e.preventDefault();
                            item.classList.toggle('active');
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
